from abc import ABC, abstractmethod
from django.utils.translation import gettext, ngettext, pgettext, npgettext


class Translator(ABC):
    @staticmethod
    @abstractmethod
    def gettext(translation_key: str, context: str = None) -> str:
        raise NotImplementedError

    @staticmethod
    @abstractmethod
    def gettext_plural(singular: str, plural: str, number: int, context: str | None = None) -> str:
        raise NotImplementedError


class TranslatorDjango(Translator):
    @staticmethod
    def gettext(translation_key: str, context: str = None) -> str:
        if context:
            return pgettext(context, translation_key)
        return gettext(translation_key)

    @staticmethod
    def gettext_plural(singular: str, plural: str, number: int, context: str | None = None) -> str:
        if context:
            return npgettext(context, singular, plural, number)
        return ngettext(singular, plural, number)
