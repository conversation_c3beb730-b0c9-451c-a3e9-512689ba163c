import os

import yaml
from django.conf import settings
from lib.cache import lru_booksy_cache

from lib.serializers import safe_get
from lib.swagger.tools import include


@lru_booksy_cache(timeout=(60 * 60 * 24))
def get_fields_from_docs(model_name):
    """
    Available fields from yaml docs especially for Dynamic Fields Serializers
    """
    docs = get_docs()
    if fields := safe_get(docs, [model_name, 'properties']):
        return set(fields.keys())


@lru_booksy_cache(timeout=(60 * 60 * 24))
def get_docs():
    yaml.SafeLoader.add_constructor('!include', include)
    relative_path = ('docs', 'api', 'models.yaml')
    models_path = os.path.join(settings.PROJECT_PATH, *relative_path)
    with open(models_path, 'r') as stream:
        docs = yaml.safe_load(stream)
    return docs
