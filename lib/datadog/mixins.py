from bo_obs.datadog import set_apm_tag_in_root_span
from bo_obs.datadog.enums import BooksyTeams


def _add_booksy_teams_tag_to_root_span(booksy_teams):
    for team in booksy_teams:
        set_apm_tag_in_root_span(f'booksy_teams.{team}', True)


class BooksyTeamInRootSpanDRFMixin:
    """
    Used for establishing ownership of a given handler and assigning a team tag to the root span
    """

    booksy_teams: tuple = (BooksyTeams.UNASSIGNED,)

    def finalize_response(self, request, response, *args, **kwargs):
        _add_booksy_teams_tag_to_root_span(self.booksy_teams)
        return super().finalize_response(request, response, *args, **kwargs)


class BooksyTeamInRootSpanTornadoMixin:
    """
    Used for establishing ownership of a given handler and assigning a team tag to the root span
    """

    booksy_teams: tuple = (BooksyTeams.UNASSIGNED,)

    def on_finish(self):
        _add_booksy_teams_tag_to_root_span(self.booksy_teams)
        super().on_finish()
