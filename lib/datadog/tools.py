import logging

import requests
from django.core.cache import cache

from lib.datadog.consts import DATADOG_SYNTHETICS_IP_URL

logger = logging.getLogger('booksy.datadog')

CACHE_KEY = 'datadog_synthetic_tests_ip_addresses'


def get_datadog_synthetics_ip_ranges() -> set | None:
    if synthetic_ips := cache.get(CACHE_KEY):
        return synthetic_ips
    try:
        response = requests.get(url=DATADOG_SYNTHETICS_IP_URL, timeout=1.0)
    except requests.RequestException as err:
        logger.error('[ERROR] Datadog Synthetics IP ranges. Request exception: %s', err)
        return
    resp_synthetics = response.json().get('synthetics', None)
    if resp_synthetics:
        resp_synthetics = set(
            resp_synthetics.get('prefixes_ipv4') + resp_synthetics.get('prefixes_ipv6')
        )
        cache.set(CACHE_KEY, resp_synthetics, timeout=60 * 60 * 24 * 7)
        return resp_synthetics


def is_whitelisted_datadog_ip(headers) -> bool:
    if real_ip := headers.get('X-Real-IP'):
        ip_address = f'{real_ip}/32'
        datadog_synthetics_ip_ranges = get_datadog_synthetics_ip_ranges()
        if not datadog_synthetics_ip_ranges:
            logger.error('Could not get Datadog Synthetic Tests IP ranges.')
            return False
        if ip_address in datadog_synthetics_ip_ranges:
            return True
        logger.error('Client IP address not in valid synthetic tests IP addresses pool.')
        cache.delete(CACHE_KEY)
    return False


def is_datadog_synthetics_test(headers):
    if not headers.get('Sec-Datadog'):
        return False
    if not is_whitelisted_datadog_ip(headers):
        return False
    return True
