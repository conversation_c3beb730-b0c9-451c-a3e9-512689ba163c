from lib.gcs_dataset.tools import create_google_client_mock

BUSINESS_PATCHER_KWARGS = (
    {'target': 'webapps.search_engine_tuning.tasks.update_user_tuning_task'},
    {
        'target': 'webapps.business.business_categories.tasks'
        '.TreatmentAssignmentUTT1.assign_treatments_for_services',
        'autospec': True,
    },
    {'target': 'webapps.business.tasks.business_customer_info_delete_task'},
    {
        'target': 'google.cloud.storage.Client.from_service_account_json',
        'return_value': create_google_client_mock(),
    },
)

PATCHER_KWARGS = BUSINESS_PATCHER_KWARGS
