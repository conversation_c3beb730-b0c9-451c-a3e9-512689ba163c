import pytest

from lib.tools import (
    camel_to_kebabcase,
    camel_to_snakecase,
    camel_to_snakecase_dict,
    snake_to_camelcase,
    snake_to_camelcase_dict,
    snake_to_pascalcase,
)


@pytest.mark.parametrize(
    'test, expected',
    (
        ('test', 'Test'),
        ('test_test', 'TestTest'),
        ('__test_test__', 'TestTest'),
        ('test test', 'Test Test'),
        ('Test_Test', 'TestTest'),
        ('Test_tesT', 'TestTest'),
    ),
)
def test_snake_to_pascalcase(test, expected):
    assert expected == snake_to_pascalcase(test)


@pytest.mark.parametrize(
    'test, expected',
    (
        ('test', 'test'),
        ('test_test', 'testTest'),
        ('__test_test__', 'testTest'),
        ('test_test', 'testTest'),
        ('Test_Test', 'testTest'),
        ('Test_tesT', 'testTest'),
    ),
)
def test_snake_to_camelcase(test, expected):
    assert expected == snake_to_camelcase(test)


@pytest.mark.parametrize(
    'test, expected',
    (
        ('test', 'test'),
        ('testTest', 'test_test'),
        ('testTest0Test', 'test_test0_test'),
    ),
)
def test_camel_to_snakecase(test, expected):
    assert expected == camel_to_snakecase(test)


@pytest.mark.parametrize(
    'test, expected',
    (
        ('test', 'test'),
        ('testTest', 'test-test'),
        ('testTest0Test', 'test-test0-test'),
    ),
)
def test_camel_to_kebabcase(test, expected):
    assert expected == camel_to_kebabcase(test)


@pytest.mark.parametrize(
    'test, expected',
    (
        ({'test': {'test': 1}}, {'test': {'test': 1}}),
        ({'test_test': {'test_test': 1}}, {'testTest': {'testTest': 1}}),
    ),
)
def test_snake_to_camelcase_dict(test, expected):
    assert expected == snake_to_camelcase_dict(test)


@pytest.mark.parametrize(
    'test, expected',
    (
        ({'test': {'test': 1}}, {'test': {'test': 1}}),
        ({'testTest': {'testTest': 1}}, {'test_test': {'test_test': 1}}),
    ),
)
def test_camel_to_snakecase_dict(test, expected):
    assert expected == camel_to_snakecase_dict(test)
