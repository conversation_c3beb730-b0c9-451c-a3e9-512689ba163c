import datetime

import pytest

from lib.datetime_utils import format_timedelta


@pytest.mark.parametrize(
    'delta, expected',
    (
        (datetime.timedelta(hours=6, minutes=16, seconds=26), '06:16:26'),
        (-datetime.timedelta(hours=6, minutes=16, seconds=26), '-06:16:26'),
        (datetime.timedelta(days=2, hours=6), '2_06:00:00'),
        (-datetime.timedelta(days=2, hours=6), '-2_06:00:00'),
    ),
)
def test_format_timedelta(delta, expected):
    assert (
        format_timedelta(
            delta,
        )
        == expected
    )
