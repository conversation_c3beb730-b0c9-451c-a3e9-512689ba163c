import pytest
from django.test.utils import override_settings

from country_config import Country, CountryConfig
from lib.unicode_utils import (
    calculate_sms_parts,
    force_unicode,
    is_sms_unicode,
    normalize_text,
    sms_dediacrit,
    text_slug,
    unicode_letter_split,
    unicode_smart_truncate,
    preliminary_dediacrit_replace,
)


# non existing language
@override_settings(LANGUAGE_CODE='en-us')
def test_sms_dediacrit_basic_1():
    assert sms_dediacrit('ĄĆĘ') == (True, 'ACE')


# euro character
@override_settings(LANGUAGE_CODE='en-us')
def test_sms_dediacrit_basic_2():
    assert sms_dediacrit('€') == (True, 'EUR')


# different currency characters
@override_settings(LANGUAGE_CODE='en-us')
def test_sms_dediacrit_basic_3():
    assert sms_dediacrit('£$¥') == (True, 'PS$Y=')


@override_settings(API_COUNTRY=Country.GB, COUNTRY_CONFIG=CountryConfig(Country.GB))
def test_sms_dediacrit_basic_en_gb_pound():
    assert sms_dediacrit('£$¥') == (True, 'GBP$Y=')


@override_settings(LANGUAGE_CODE='en-us')
def test_sms_dediacrit_en():
    assert sms_dediacrit('ABC') == (True, 'ABC')


# TESTS sms_dediacrit
@override_settings(LANGUAGE_CODE='pl-pl')
def test_sms_dediacrit_pl_1():
    assert sms_dediacrit('ĄĘŃ') == (True, 'AEN')


@override_settings(LANGUAGE_CODE='pl-pl')
def test_sms_dediacrit_pl_2():
    assert sms_dediacrit('barberzy mężczyźni') == (True, 'barberzy mezczyzni')


@override_settings(LANGUAGE_CODE='ru-r')
def test_sms_dediacrit_ru_1():
    assert sms_dediacrit('Компьютер') == (True, 'Компьютер')


@override_settings(LANGUAGE_CODE='en-us')
def test_sms_dediacrit_ru_2():
    assert sms_dediacrit('Компьютер') == (True, "Komp'iuter")


@override_settings(LANGUAGE_CODE='zh-zh')
def test_sms_dediacrit_ch_1():
    # Bei Jing  (capital of China)
    assert sms_dediacrit('北京 (capital of China)') == (True, '北京 (capital of China)')


@override_settings(LANGUAGE_CODE='en-us')
def test_sms_dediacrit_ch_2():
    assert sms_dediacrit('北京 (capital of China)') == (True, 'Bei Jing  (capital of China)')


@override_settings(LANGUAGE_CODE='fr-fr')
def test_sms_dediacrit_fr():
    assert sms_dediacrit('ÄÇÉ') == (True, 'ACE')


@override_settings(LANGUAGE_CODE='pt-pt')
def test_sms_dediacrit_pt():
    assert sms_dediacrit('Voàscê cê') == (True, 'Voasce ce')


@override_settings(LANGUAGE_CODE='pl-pl')
def test_sms_dediacrit_uk_1():
    assert sms_dediacrit('Україна') == (True, 'Ukrayina')


@override_settings(LANGUAGE_CODE='uk-ua')
def test_sms_dediacrit_uk_2():
    assert sms_dediacrit('Україна') == (True, 'Україна')


# language code that not in settings.LANGUAGES
@override_settings(LANGUAGE_CODE='it-it')
def test_sms_dediacrit_non_language_code():
    assert sms_dediacrit('ÄÇÉ') == (True, 'ÄÇÉ')


# TESTS calculate_sms_parts
data_calculate_sms_parts = (
    ('', 1),
    ('a' * 120, 1),
    ('a' * 160, 1),
    ('a' * 161, 2),
    ('a' * 306, 2),
    ('a' * 307, 3),
    ('|' * 79, 1),
    ('|' * 80, 1),
    ('|' * 81, 2),
    ('|' * 150, 2),
    ('|' * 154, 3),
    ('ą' * 60, 1),
    ('ą' * 70, 1),
    ('ą' * 71, 2),
    ('ą' * 134, 2),
    ('ą' * 135, 3),
)


@pytest.mark.parametrize('string, expected', data_calculate_sms_parts)
def test_calculate_sms_parts(string, expected):
    assert calculate_sms_parts(string) == expected


# TESTS text_slug
data_text_slug = (
    ('foo bar', 'foo-bar'),
    ('FOO  Bar', 'foo-bar'),
    ('-foo bar-', 'foo-bar'),
    ('foo...bar', 'foo-bar'),
    ('żółć', 'zolc'),
)


@pytest.mark.parametrize('string, expected', data_text_slug)
def test_text_slug(string, expected):
    assert text_slug(string) == expected


# TESTS force_unicode
data_text_slug = (
    ('a', str),
    ('a', str),
    (7, str),
    ('ą', str),
    ('京', str),
)


@pytest.mark.parametrize('string, expected', data_text_slug)
def test_force_unicode(string, expected):
    isinstance(force_unicode(string), expected)


# TESTS is_sms_unicode
@override_settings(LANGUAGE_CODE='en-us')
def test_is_sms_unicode():
    assert not is_sms_unicode('foo bar baz')
    assert not is_sms_unicode('äé')
    assert is_sms_unicode('ąę')


# TESTS unicode_letter_split
data_unicode_letter_split = (
    ('', ['', '', '']),
    ('!', ['', '', '!']),
    ("a", ['', 'a', '']),
    ('foo bar', ['', 'foo', ' ', 'bar', '']),
    ('<foo-bar>', ['', '<foo', '-', 'bar>', '']),
    ('<bęć-ówć>', ['', '<bęć', '-', 'ówć>', '']),
    ('"bęć`ówć"', ['', '', '"', 'bęć`ówć', '"']),
)


@pytest.mark.parametrize('string, expected', data_unicode_letter_split)
def test_unicode_letter_split(string, expected):
    assert unicode_letter_split(string) == expected


# TESTS unicode_smart_truncate
data_unicode_smart_truncate = (
    (('foo bar baz', 10, 5), 'foo bar'),
    (('fooXbarXbaz', 10, 5), 'fooXbarXba'),
    (('foo barXbaz', 10, 5), 'foo barXba'),
    (('foo barXbaz', 10, 8), 'foo'),
    (('foo barXbaz', 10, 5, '...'), 'foo bar...'),
    (('fooX barXbaz', 10, 5, '...'), 'fooX ba...'),
    (('fooXY barXbaz', 10, 5, '...'), 'fooXY'),
    (
        ('Zakład kosmetyczny Joanna Kowalska-Brzęczyszczykiewicz', 30, 5),
        'Zakład kosmetyczny Joanna',
    ),
    (('Joanna Kowalska-Brzęczyszczykiewicz', 30, 5), 'Joanna Kowalska-Brzęczyszczyki'),
    (('Joanna Kowalska-Brzęczyszczykiewicz', 30, 5, '...'), 'Joanna Kowalska-Brzęczyszcz...'),
    (('foo 123', 10, 5), 'foo 123'),
    (('foo 123\u2122', 10, 5), 'foo 123\u2122'),
    (('foo 123\u2122 abc', 10, 5), 'foo 123\u2122'),
    (('foo 123\u2122 abc', 10, 1), 'foo 123\u2122 a'),
    (('foo 123 \u2122', 10, 1), 'foo 123 \u2122'),
    (('foo 123 \u2122 abc', 10, 1), 'foo 123 \u2122'),
    (('foo 123ą abc', 10, 5), 'foo 123ą'),
    (('foo 123ą abc', 10, 1), 'foo 123ą a'),
    (('foo 123ą bcdddd', 10, 5), 'foo 123ą'),
)


@pytest.mark.parametrize('unicode_letter_split_args, expected', data_unicode_smart_truncate)
def test_unicode_smart_truncate(unicode_letter_split_args, expected):
    assert unicode_smart_truncate(*unicode_letter_split_args) == expected


# TESTS normalize_text
data_normalize_text = (
    ('\n\nfoo\n\nbar\n\n', 'foo bar'),
    ('\n\nfoo\n\nbar\n\n', 'foo bar'),
    ('foo! bar\n\n', 'foo! bar'),
    ('foo    bar', 'foo bar'),
)


@pytest.mark.parametrize('string, expected', data_normalize_text)
def test_normalize_text(string, expected):
    assert normalize_text(string) == expected


@override_settings(API_COUNTRY=Country.GB, COUNTRY_CONFIG=CountryConfig(Country.GB))
def test_preliminary_dediacrit_replace():
    assert preliminary_dediacrit_replace('£120') == 'GBP120'
