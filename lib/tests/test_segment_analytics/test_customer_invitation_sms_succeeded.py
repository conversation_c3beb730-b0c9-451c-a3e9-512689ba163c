import pytest
from django.utils import timezone
from mock import patch
from model_bakery import baker

from lib.segment_analytics import get_segment_api
from lib.segment_analytics.api import AnalyticsApi
from lib.test_utils import create_subbooking
from webapps.booking.enums import BookingType
from webapps.business.models import Business
from webapps.notification.models import NotificationHistory


@pytest.mark.django_db
@patch.object(AnalyticsApi, 'make_call')
def test_customer_invitation_sms_succeeded_correct(make_call_mock):
    business = baker.make(Business)
    segment_api = get_segment_api(business)

    phone = '+***********'
    task_id = 'invitation:invitation_sms:business_id={}'.format(business.id)
    booking_type = BookingType.CUSTOMER_BOOKING

    booking, *_ = create_subbooking(
        business=business,
        booking_kws=dict(
            customer_phone=phone,
        ),
    )

    notification1 = baker.make(
        NotificationHistory,
        task_id=task_id,
        recipient_phone=phone,
    )
    notification1.created = timezone.now() - timezone.timedelta(days=8)
    notification1.save()

    segment_api.customer_invitation_sms_succeeded(booking, booking_type)

    call_args = make_call_mock.call_args_list[0][1]

    assert make_call_mock.call_count == 1
    assert call_args['props']['customerPhoneNumber'] == '+***********'
    assert call_args['props']['bookingType'] == BookingType.CUSTOMER_BOOKING


@pytest.mark.django_db
@patch.object(AnalyticsApi, 'make_call')
def test_customer_invitation_sms_succeeded_too_old_invitation(make_call_mock):
    business = baker.make(Business)
    segment_api = get_segment_api(business)

    phone = '+***********'
    task_id = 'invitation:invitation_sms:business_id={}'.format(business.id)
    booking_type = BookingType.CUSTOMER_BOOKING

    booking, *_ = create_subbooking(
        business=business,
        booking_kws=dict(
            customer_phone=phone,
        ),
    )

    notification1 = baker.make(
        NotificationHistory,
        task_id=task_id,
        recipient_phone=phone,
    )
    notification1.created = timezone.now() - timezone.timedelta(days=15)
    notification1.save()

    segment_api.customer_invitation_sms_succeeded(booking, booking_type)
    assert make_call_mock.call_count == 0


@pytest.mark.django_db
@patch.object(AnalyticsApi, 'make_call')
def test_customer_invitation_sms_succeeded_booking_after_invit(make_call_mock):
    business = baker.make(Business)
    segment_api = get_segment_api(business)

    phone = '+***********'
    task_id = 'invitation:invitation_sms:business_id={}'.format(business.id)
    booking_type = BookingType.CUSTOMER_BOOKING

    # booking after invit
    create_subbooking(
        business=business,
        created=timezone.now() - timezone.timedelta(days=2),
        booking_kws=dict(
            customer_phone=phone,
        ),
    )

    booking, *_ = create_subbooking(
        business=business,
        booking_kws=dict(
            customer_phone=phone,
        ),
    )

    notification = baker.make(
        NotificationHistory,
        task_id=task_id,
        recipient_phone=phone,
    )
    notification.created = timezone.now() - timezone.timedelta(days=8)
    notification.save()

    segment_api.customer_invitation_sms_succeeded(booking, booking_type)

    assert make_call_mock.call_count == 0


@pytest.mark.django_db
@patch.object(AnalyticsApi, 'make_call')
def test_customer_invitation_sms_succeeded_booking_be4_invit(make_call_mock):
    business = baker.make(Business)
    segment_api = get_segment_api(business)

    phone = '+***********'
    task_id = 'invitation:invitation_sms:business_id={}'.format(business.id)
    booking_type = BookingType.CUSTOMER_BOOKING

    # booking after invit
    create_subbooking(
        business=business,
        created=timezone.now() - timezone.timedelta(days=10),
        booking_kws=dict(
            customer_phone=phone,
        ),
    )

    booking, *_ = create_subbooking(
        business=business,
        booking_kws=dict(
            customer_phone=phone,
        ),
    )

    notification = baker.make(
        NotificationHistory,
        task_id=task_id,
        recipient_phone=phone,
    )
    notification.created = timezone.now() - timezone.timedelta(days=8)
    notification.save()

    segment_api.customer_invitation_sms_succeeded(booking, booking_type)

    assert make_call_mock.call_count == 0


@pytest.mark.django_db
@patch.object(AnalyticsApi, 'make_call')
def test_customer_invitation_sms_succeeded_wrong_booking_type(make_call_mock):
    business = baker.make(Business)
    segment_api = get_segment_api(business)

    phone = '+***********'
    task_id = 'invitation:invitation_sms:business_id={}'.format(business.id)
    booking_type = BookingType.WALK_IN

    booking, *_ = create_subbooking(
        business=business,
        booking_kws=dict(
            business=business,
            customer_phone=phone,
        ),
    )

    notification = baker.make(
        NotificationHistory,
        task_id=task_id,
        recipient_phone=phone,
    )
    notification.created = timezone.now() - timezone.timedelta(days=8)
    notification.save()

    segment_api.customer_invitation_sms_succeeded(booking, booking_type)

    assert make_call_mock.call_count == 0


@pytest.mark.django_db
@patch.object(AnalyticsApi, 'make_call')
def test_customer_invitation_sms_succeeded_wrong_business(make_call_mock):
    business = baker.make(Business)
    business2 = baker.make(Business)
    segment_api = get_segment_api(business)

    phone = '+***********'
    task_id = 'invitation:invitation_sms:business_id={}'.format(business2.id)
    booking_type = BookingType.CUSTOMER_BOOKING

    booking, *_ = create_subbooking(
        business=business,
        booking_kws=dict(
            business=business,
            customer_phone=phone,
        ),
    )

    notification1 = baker.make(
        NotificationHistory,
        task_id=task_id,
        recipient_phone=phone,
    )
    notification1.created = timezone.now() - timezone.timedelta(days=8)
    notification1.save()

    segment_api.customer_invitation_sms_succeeded(booking, booking_type)

    assert make_call_mock.call_count == 0
