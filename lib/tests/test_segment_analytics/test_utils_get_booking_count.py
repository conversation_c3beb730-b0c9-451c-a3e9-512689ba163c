import datetime
from functools import partial

import pytest
from model_bakery import baker

from lib.segment_analytics.utils import get_booking_count
from lib.test_utils import create_subbooking
from lib.tools import tznow
from webapps.booking.models import (
    Appointment,
    BookingSources,
)
from webapps.booking.tests.utils import create_appointment
from webapps.business.models import Business
from webapps.consts import GOOGLE


@pytest.mark.django_db
def test_get_booking_count():
    business = baker.make(Business)
    source = baker.make(BookingSources, name='Web')
    source_google = baker.make(BookingSources, name=GOOGLE)

    create_customer_booking = partial(
        create_subbooking,
        business=business,
        source=source,
        booking_type=Appointment.TYPE.CUSTOMER,
    )
    create_biz_booking = partial(
        create_customer_booking,
        booking_type=Appointment.TYPE.BUSINESS,
    )

    create_appointment([{}, {}], business=business, source=source, type=Appointment.TYPE.BUSINESS)
    create_biz_booking()
    create_customer_booking()
    create_customer_booking()
    create_customer_booking(source=source_google)  # pylint: disable=redundant-keyword-arg
    create_customer_booking(source=source_google)  # pylint: disable=redundant-keyword-arg

    count = get_booking_count(business)

    assert count.bb == 2
    assert count.cb == 4
    assert count.cb_google == 2
    assert count.all == 6


@pytest.mark.django_db
def test_get_booking_count_for_date():
    business = baker.make(Business)
    business2 = baker.make(Business)
    source_google = baker.make(BookingSources, name=GOOGLE)

    create_appointment(business=business, type=Appointment.TYPE.BUSINESS)
    create_appointment([{}, {}], business=business, type=Appointment.TYPE.BUSINESS)
    create_appointment([{}, {}], business=business2, type=Appointment.TYPE.BUSINESS)

    create_appointment(business=business, type=Appointment.TYPE.CUSTOMER)
    create_appointment(business=business, type=Appointment.TYPE.CUSTOMER)
    create_appointment(business=business, type=Appointment.TYPE.CUSTOMER, source=source_google)
    create_appointment(business=business, type=Appointment.TYPE.CUSTOMER, source=source_google)
    create_appointment(business=business2, type=Appointment.TYPE.CUSTOMER)
    create_appointment(business=business2, type=Appointment.TYPE.CUSTOMER)
    create_appointment(business=business2, type=Appointment.TYPE.CUSTOMER)

    yesterday = business2.tznow - datetime.timedelta(days=1)
    Appointment.objects.filter(business=business2).update(created=yesterday)

    count = get_booking_count(business, tznow())

    assert count.bb == 2
    assert count.cb == 4
    assert count.cb_google == 2
    assert count.all == 6
