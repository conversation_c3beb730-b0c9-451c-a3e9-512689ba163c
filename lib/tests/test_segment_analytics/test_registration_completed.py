import pytest
from mock import (
    patch,
    MagicMock,
)
from model_bakery import baker

from lib.segment_analytics import get_segment_api
from lib.segment_analytics.api import AnalyticsApi
from lib.tools import tznow
from webapps.booking.models import BookingSources
from webapps.kill_switch.models import KillSwitch


@pytest.mark.django_db
@patch.object(AnalyticsApi, 'make_call')
def test_without_referral_code(
    make_call_mock,
):
    source = baker.prepare(BookingSources)
    business = MagicMock()
    business.active_from = tznow()
    segment_api = get_segment_api(business, source)
    segment_api.registration_completed()

    now = tznow()
    cohort_month = now.strftime("%Y.%m")
    cohort_week = now.strftime("%Y.%W")

    assert make_call_mock.call_count == 1

    traits = make_call_mock.mock_calls[0][2]['traits']

    assert traits['cohortMonth'] == cohort_month
    assert traits['cohortWeek'] == cohort_week

    props = make_call_mock.mock_calls[0][2]['props']

    assert props['cohortMonth'] == cohort_month
    assert props['cohortWeek'] == cohort_week

    # test OLD_MARTECH_ANALYTICS killswitch
    baker.make(
        KillSwitch,
        name=KillSwitch.MarTech.OLD_MARTECH_ANALYTICS,
        is_killed=True,
    )

    segment_api = get_segment_api(business, source)
    segment_api.registration_completed()

    assert make_call_mock.call_count == 1
