from datetime import timedelta

import pytest
from mock import (
    MagicMock,
    patch,
)
from model_bakery import baker

from lib.segment_analytics import get_segment_api
from lib.segment_analytics.api import AnalyticsApi
from lib.tools import tznow
from webapps.booking.models import Appointment
from webapps.business.models import Business


@pytest.mark.django_db
@patch.object(AnalyticsApi, 'make_call')
def test_specific_sum_of_appointments_days_after(make_call_mock):
    active_from = tznow() - timedelta(days=30)
    business = baker.prepare(Business, active_from=active_from)

    booking_source = MagicMock()
    booking = MagicMock()
    booking.appointment.type = Appointment.TYPE.CUSTOMER
    booking_counts = MagicMock()
    booking_counts.cb = 5

    segment_api = get_segment_api(business, booking_source)
    segment_api._specific_sum_of_appointments(  # pylint: disable=protected-access
        booking,
        booking_source,
        booking_counts=booking_counts,
    )

    assert make_call_mock.call_count == 1

    call_args = make_call_mock.call_args_list[0][1]
    assert call_args['props']['daysAfterMerchantCompleteRegistration'] == 30


@pytest.mark.django_db
@patch.object(AnalyticsApi, 'make_call')
def test_specific_sum_of_appointments_days_no_activate_from(make_call_mock):
    active_from = tznow() - timedelta(days=20)
    business = baker.prepare(Business, created=active_from)

    booking_source = MagicMock()
    booking = MagicMock()
    booking.appointment.type = Appointment.TYPE.CUSTOMER
    booking_counts = MagicMock()
    booking_counts.cb = 10

    segment_api = get_segment_api(business, booking_source)
    segment_api._specific_sum_of_appointments(  # pylint: disable=protected-access
        booking,
        booking_source,
        booking_counts=booking_counts,
    )

    assert make_call_mock.call_count == 1

    call_args = make_call_mock.call_args_list[0][1]
    assert call_args['props']['daysAfterMerchantCompleteRegistration'] == 20
