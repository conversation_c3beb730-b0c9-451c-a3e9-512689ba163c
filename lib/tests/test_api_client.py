import typing as t
import logging

from unittest.mock import patch

import pytest
import requests

from lib.api_client import APIClient, APIError


def test_api_client_doesnt_retry():
    logger = logging.getLogger('fake.logger')

    class CustomClient(APIClient):
        @property
        def base_url(self):
            return 'http://localhost:8080'

        @property
        def logger(self):
            return logger

        @property
        def error(self) -> t.Type[APIError]:
            return APIError

    request_counter = 0

    def fail_with_timeout(*args, **kwargs):
        nonlocal request_counter

        request_counter += 1

        raise requests.exceptions.Timeout

    with patch('requests.request', side_effect=fail_with_timeout):
        client = CustomClient()

        with pytest.raises(APIError):
            client.get('/some_endpoint')

    assert request_counter == 1
