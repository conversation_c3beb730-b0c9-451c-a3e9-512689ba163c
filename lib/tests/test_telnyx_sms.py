import collections
from unittest.mock import Mock
from uuid import uuid4

import pytest
import responses
from django.test import override_settings
from mock import MagicMock, patch
from rest_framework.status import HTTP_502_BAD_GATEWAY, HTTP_409_CONFLICT

from country_config.enums import Country
from lib.booksy_sms import PhoneNumber, parse_phone_number, telnyx_send_sms
from lib.booksy_sms.enums import SmsGroup, SMSServiceStatus
from lib.booksy_sms.telnyx import MAX_TRIES, RETRY_STATUS_CODES, _get_from_name
from lib.feature_flag.feature import SMSProfilesPerCountryFlag
from lib.feature_flag.feature.notification import (
    UseRequestClientRetryTelnyxFlag,
    TelnyxMoveToSettingsSMSProfilesPerCountryFlag,
)
from lib.tests.utils import override_feature_flag, override_eppo_feature_flag

# pylint: disable=redefined-outer-name
from webapps.notification.enums import NotificationService

TEST_PHONE_GLOBAL_SHORT = "+1123123313123"
COUNTRY_CODES_TO_TEST = ('us', 'pl', 'ca')
ERROR_CODES_TO_TEST = (400, 401, 402, 403, 404, 408, 429, 500, 503)
PhoneNumberMock = collections.namedtuple('PhoneNumberMock', 'global_short')
TELNYX_ID = "3fa85f64-5717-4562-b3fc-2c963f66afa6"
TELNYX_MESSAGES_API_URL = 'https://api.telnyx.com/v2/messages'


def get_telnyx_api_basic_response():
    return {
        "completed_at": None,
        "cost": None,
        "direction": "outbound",
        "encoding": "GSM-7",
        "errors": [],
        "from": {"carrier": "TELNYX LLC", "line_type": "VoIP", "phone_number": "+18445550001"},
        "id": TELNYX_ID,
        "media": [],
        "messaging_profile_id": "dd50eba1-a0c0-4563-9925-b25e842a7cb6",
        "parts": 1,
        "received_at": "2019-01-23T18:10:02.574Z",
        "record_type": "message",
        "sent_at": None,
        "subject": "From Telnyx!",
        "tags": ["Greetings"],
        "text": "Hello, World!",
        "to": [
            {
                "carrier": "T-MOBILE USA, INC.",
                "line_type": "Wireless",
                "phone_number": TEST_PHONE_GLOBAL_SHORT,
                "status": "queued",
            }
        ],
        "type": "MMS",
        "valid_until": None,
        "webhook_failover_url": "https://backup.example.com/hooks",
        "webhook_url": "https://www.example.com/hooks",
    }


def get_telnyx_test_api_response(status):
    basic_response = get_telnyx_api_basic_response()
    basic_response["to"][0]["status"] = status
    return basic_response


def get_test_error(code=00000, detail='Test error'):
    return {
        'code': str(code),
        'title': 'Test error',
        'detail': detail,
        'meta': {'url': f'https://developers.telnyx.com/docs/overview/errors/{code}'},
        'source': {'pointer': '/to'},
    }


@pytest.fixture
def telnyx_send_sms_mocked(phone_number):
    @responses.activate
    def _telnyx_send_sms(
        telnyx_settings, status_code_returned, delivery_status, errors=None, metadata=None
    ):

        if status_code_returned == 200:
            response_data = {"data": get_telnyx_test_api_response(delivery_status)}
        else:
            response_data = {"errors": errors or []}
        responses.add(
            responses.POST,
            TELNYX_MESSAGES_API_URL,
            status=status_code_returned,
            json=response_data,
        )
        message = f"Test message {uuid4()}"
        with patch('time.sleep', return_value=None):
            return telnyx_send_sms(
                service_settings=telnyx_settings,
                phone_number=phone_number,
                message=message,
                metadata=metadata or {},
            )

    return _telnyx_send_sms


def get_telnyx_settings(country_code='us'):
    settings = {
        'service': NotificationService.TELNYX,
        'api_key': 'abcd',
    }
    if country_code != 'us':
        settings["from_name"] = "TestName"
    return settings


@pytest.fixture
def phone_number():
    mock = MagicMock(spec=PhoneNumber)
    mock.global_short = TEST_PHONE_GLOBAL_SHORT
    mock.country = 'us'

    return mock


@pytest.mark.parametrize('country_code', COUNTRY_CODES_TO_TEST)
@pytest.mark.parametrize(
    'delivery_status, status_code, expected_fields',
    (
        ('delivered', 200, {"messaging_profile", "messaging_profile_id"}),
        (None, 400, set()),
    ),
)
def test_telnyx_send_sms_gives_proper_metadata_fields(
    telnyx_send_sms_mocked,
    country_code,
    delivery_status,
    status_code,
    expected_fields,
):
    telnyx_settings = get_telnyx_settings(country_code)
    result = telnyx_send_sms_mocked(telnyx_settings, status_code, delivery_status)
    assert set(result.metadata.keys()) == expected_fields


@pytest.mark.parametrize('country_code', COUNTRY_CODES_TO_TEST)
def test_telnyx_send_sms_success(telnyx_send_sms_mocked, country_code):
    telnyx_settings = get_telnyx_settings(country_code)
    result = telnyx_send_sms_mocked(telnyx_settings, 200, 'delivered')
    assert result.status == SMSServiceStatus.SUCCESS
    assert result.phone == TEST_PHONE_GLOBAL_SHORT
    assert result.external_id == TELNYX_ID


@pytest.mark.parametrize('country_code', COUNTRY_CODES_TO_TEST)
@pytest.mark.parametrize('error_code', ERROR_CODES_TO_TEST)
def test_telnyx_send_sms_error(
    telnyx_send_sms_mocked,
    country_code,
    error_code,
):
    telnyx_settings = get_telnyx_settings(country_code)
    result = telnyx_send_sms_mocked(telnyx_settings, error_code, None, [get_test_error()])
    assert result.status == SMSServiceStatus.ERROR
    assert result.error_type == 'telnyx_exception'
    assert result.error_details


@responses.activate
def test_telnyx_send_sms_invalid_reponse():
    responses.add(
        responses.POST,
        TELNYX_MESSAGES_API_URL,
        status=HTTP_502_BAD_GATEWAY,
        json='Invalid response obj...',
    )
    result = telnyx_send_sms(
        service_settings=get_telnyx_settings('us'),
        phone_number=parse_phone_number('+12025550187'),
        message="Test Message",
        metadata={},
    )
    assert result.status == SMSServiceStatus.ERROR
    assert result.error_type == 'telnyx_exception'
    assert result.error_details


def with_from_field_body_matcher(body):
    return "from" in body.body, "from not in body"


def without_from_field_body_matcher(body):
    return "from" not in body.body, "from in body"


@responses.activate
def test_telnyx_send_sms_retry_error_40301(
    telnyx_send_sms_mocked,
    phone_number,
):
    responses.add(
        responses.POST,
        TELNYX_MESSAGES_API_URL,
        status=400,
        json={
            "errors": [
                get_test_error(
                    code=40301,
                    detail=(
                        'The requested destination +1403123123 is currently'
                        ' unsupported for the type of traffic originated by'
                        ' alphanumeric sender ID'
                    ),
                )
            ],
        },
        match=[with_from_field_body_matcher],
    )
    responses.add(
        responses.POST,
        TELNYX_MESSAGES_API_URL,
        status=200,
        json={"data": get_telnyx_test_api_response('delivered')},
        match=[without_from_field_body_matcher],
    )
    message = f"Test message {uuid4()}"
    telnyx_settings = get_telnyx_settings('other')
    with patch('time.sleep', return_value=None):
        result = telnyx_send_sms(telnyx_settings, phone_number, message)
    assert result.status == SMSServiceStatus.ERROR
    assert len(responses.calls) == 1
    assert responses.calls[0].response.status_code == 400


@pytest.mark.parametrize('retry_status_code', RETRY_STATUS_CODES)
@responses.activate
def test_telnyx_retries_on_error(telnyx_send_sms_mocked, phone_number, retry_status_code):
    response_data = {"errors": [get_test_error()]}
    responses.add(
        responses.POST,
        TELNYX_MESSAGES_API_URL,
        status=retry_status_code,
        json=response_data,
    )
    message = f"Test message {uuid4()}"
    telnyx_settings = get_telnyx_settings('other')
    with patch('time.sleep', return_value=None):
        telnyx_send_sms(telnyx_settings, phone_number, message)

    assert len(responses.calls) == MAX_TRIES


@override_eppo_feature_flag({UseRequestClientRetryTelnyxFlag.flag_name: True})
@responses.activate
def test_telnyx_retries_on_error_use_request_client(telnyx_send_sms_mocked, phone_number):
    response_data = {"errors": [get_test_error()]}
    responses.add(
        responses.POST,
        TELNYX_MESSAGES_API_URL,
        status=HTTP_409_CONFLICT,
        json=response_data,
    )
    message = f"Test message {uuid4()}"
    telnyx_settings = get_telnyx_settings('other')
    with patch('time.sleep', return_value=None):
        telnyx_send_sms(telnyx_settings, phone_number, message)

    assert len(responses.calls) == MAX_TRIES


@pytest.mark.parametrize(
    'message_type, profile',
    (
        *(
            (group.value, 'System')
            for group in (
                SmsGroup.SYSTEM,
                SmsGroup.CUSTOMER_REGISTRATION,
            )
        ),
        *(
            (group.value, 'Other')
            for group in (
                SmsGroup.MARKETING,
                SmsGroup.INVITATION,
                SmsGroup.WAITLIST,
            )
        ),
        (None, 'Other'),
    ),
)
def test_telnyx_send_sms_messaging_profile(
    telnyx_send_sms_mocked,
    message_type,
    profile,
):
    telnyx_settings = get_telnyx_settings()
    result = telnyx_send_sms_mocked(
        telnyx_settings,
        200,
        'delivered',
        metadata=dict(message_type=message_type),
    )
    assert result.metadata['messaging_profile'] == profile


@override_feature_flag({SMSProfilesPerCountryFlag.flag_name: True})
@pytest.mark.parametrize(
    'message_type, profile',
    (
        *(
            (group.value, 'US System Messages')
            for group in (
                SmsGroup.SYSTEM,
                SmsGroup.WAITLIST,
            )
        ),
        *(
            (group.value, 'US Marketing Messages')
            for group in (
                SmsGroup.MARKETING,
                SmsGroup.OTHER,
            )
        ),
        # For now we USE_TELNYX_US_INVITES_IN_MARKETING_PROFILE
        (SmsGroup.INVITATION.value, 'US Invitation Messages'),
        (SmsGroup.CUSTOMER_REGISTRATION.value, 'US OTP Messages'),
        (None, 'US Marketing Messages'),
    ),
)
# Edge case when we can't send alphanumeric to us and ca from other profiles than us
@pytest.mark.parametrize('api_country', [Country.US, Country.CA, Country.PL])
def test_telnyx_send_sms_new_messaging_profiles(
    telnyx_send_sms_mocked,
    message_type,
    profile,
    api_country,
):
    override_settings(API_COUNTRY=api_country)
    telnyx_settings = get_telnyx_settings()
    result = telnyx_send_sms_mocked(
        telnyx_settings,
        200,
        'delivered',
        metadata=dict(message_type=message_type),
    )
    assert result.metadata['messaging_profile'] == profile


@override_feature_flag({SMSProfilesPerCountryFlag.flag_name: False})
@override_eppo_feature_flag({TelnyxMoveToSettingsSMSProfilesPerCountryFlag.flag_name: True})
@pytest.mark.parametrize(
    'message_type, profile',
    (
        (SmsGroup.SYSTEM.value, 'System'),
        *(
            (group.value, 'Other')
            for group in (
                SmsGroup.MARKETING,
                SmsGroup.OTHER,
                SmsGroup.WAITLIST,
            )
        ),
        (SmsGroup.INVITATION.value, 'US Invitation Messages'),
        (SmsGroup.CUSTOMER_REGISTRATION.value, 'US OTP Messages'),
        (None, 'Other'),
    ),
)
@pytest.mark.parametrize('api_country', [Country.US, Country.CA, Country.PL])
def test_telnyx_send_sms_country_profile_usage_setting(
    telnyx_send_sms_mocked,
    message_type,
    profile,
    api_country,
):
    override_settings(API_COUNTRY=api_country)
    telnyx_settings = get_telnyx_settings()
    result = telnyx_send_sms_mocked(
        telnyx_settings,
        200,
        'delivered',
        metadata=dict(message_type=message_type),
    )
    assert result.metadata['messaging_profile'] == profile


@pytest.mark.parametrize(
    'phone_number',
    [
        parse_phone_number("+85023812134"),
        parse_phone_number("+25712345678"),
        parse_phone_number("+8618411632866"),
    ],
)
def test_telnyx_send_sms_to_not_whitelisted_country(telnyx_send_sms_mocked, phone_number):
    telnyx_settings = get_telnyx_settings()

    result = telnyx_send_sms_mocked(
        telnyx_settings,
        200,
        'delivered',
    )
    assert result.metadata['messaging_profile'] == 'ROW countries'


@pytest.mark.parametrize(
    'phone_number',
    [
        parse_phone_number("+4812345678"),
        parse_phone_number("+12025550187"),
    ],
)
def test_telnyx_send_sms_to_whitelisted_country(telnyx_send_sms_mocked, phone_number):
    telnyx_settings = get_telnyx_settings()

    result = telnyx_send_sms_mocked(
        telnyx_settings,
        200,
        'delivered',
    )
    assert result.metadata['messaging_profile'] == 'Other'


@patch('webapps.message_blast.ports.block_phone_number')
def test_telnyx_send_sms_block_phone_number_on_stop_gateway_error(
    mocked_block_phone_number, telnyx_send_sms_mocked
):
    # If a user opted-out by sending the "STOP" message, then the request to send an SMS to that
    # user will be blocked at the gateway level and no webhook will be delivered.
    # https://developers.telnyx.com/docs/v2/messaging/configuration-and-limitations/configuration-\
    # and-usage#auto-responses
    telnyx_settings = get_telnyx_settings()
    telnyx_send_sms_mocked(
        telnyx_settings,
        status_code_returned=409,
        delivery_status='',
        errors=[
            {
                "code": "40300",
                "detail": "Messages cannot be sent from '' to '' due to an existing block rule.",
                "meta": {"url": "https://developers.telnyx.com/docs/overview/errors/40300"},
                "title": "Blocked due to STOP message",
            }
        ],
    )
    mocked_block_phone_number.assert_called_once_with(
        TEST_PHONE_GLOBAL_SHORT, NotificationService.TELNYX
    )


@override_settings(REGULATED_ALPHANUMERIC_SENDER_COUNTRIES=[Country.CA])
@pytest.mark.parametrize(
    'country_code, phone_number, expected_from_name',
    [
        (Country.CA, parse_phone_number('+14509125582'), None),
        (Country.US, parse_phone_number('+12564188575'), None),
        (Country.PL, parse_phone_number('+48787787877'), 'TestName'),
    ],
)
def test__get_from_name(country_code, phone_number, expected_from_name):
    assert _get_from_name(get_telnyx_settings(country_code), phone_number) == expected_from_name


@override_settings(REGULATED_ALPHANUMERIC_SENDER_COUNTRIES=[Country.CA], API_COUNTRY=Country.US)
@pytest.mark.parametrize(
    'country_code, phone_number, expected_from_name',
    [
        (Country.CA, parse_phone_number('+14509125582'), False),
        (Country.US, parse_phone_number('+12564188575'), False),
        (Country.PL, parse_phone_number('+48787787877'), True),
    ],
)
def test_telnyx_send_sms_success__with_flag_on(country_code, phone_number, expected_from_name):
    telnyx_settings = get_telnyx_settings(country_code)
    with patch('telnyx.Message.create') as telnyx_message:
        telnyx_message.return_value = Mock(to=[Mock(status='sucess')])
        telnyx_send_sms(telnyx_settings, phone_number, 'Test message')

    assert telnyx_message.call_args[1]['to'] == phone_number.global_short
    assert telnyx_message.call_args[1]['text'] == 'Test message'
    assert ('from_' in telnyx_message.call_args[1].keys()) == expected_from_name
