import pytest
from django.test import TestCase
from model_bakery import baker
from pytz import UTC

from lib.timezone_hours import get_timezones_with_hour
from lib.tools import tznow
from webapps.business.models import Business
from webapps.structure.models import Region

# create datetime object
test_date_time = tznow(tz=UTC)
test_date_time = test_date_time.replace(year=2018, month=4, day=1, hour=10, minute=0)
test_date_time2 = test_date_time.replace(year=2018, month=4, day=1, hour=23, minute=0)


class TestGetBusiness(TestCase):

    @classmethod
    def setUpTestData(cls):
        list_regions = [
            'Europe/Warsaw',
            'Europe/Helsinki',
            'America/New_York',
            'Indian/Comoro',
            'America/Sao_Paulo',
        ]

        for region in list_regions:
            # business
            region = baker.make(
                Region,
                time_zone_name=region,
            )
            business = baker.make(
                Business,
                region=region,
            )
            tz = business.get_timezone()

            assert tz._long_name == region.time_zone_name  # pylint: disable=protected-access

    @pytest.mark.freeze_time(test_date_time)
    def test_now_mock(self):
        result = get_timezones_with_hour(10)
        assert result == []

        result = get_timezones_with_hour(13)
        assert set(result) == set(['Europe/Helsinki', 'Indian/Comoro'])

        result = get_timezones_with_hour(6)
        assert result == ['America/New_York']

        result = get_timezones_with_hour(12)
        assert result == ['Europe/Warsaw']

        result = get_timezones_with_hour(7)
        assert result == ['America/Sao_Paulo']

    @pytest.mark.freeze_time(test_date_time2)
    def test_now_mock_2(self):
        result = get_timezones_with_hour(10)
        assert result == []

        result = get_timezones_with_hour(2)
        assert set(result) == set(['Europe/Helsinki', 'Indian/Comoro'])

        result = get_timezones_with_hour(19)
        assert result == ['America/New_York']

        result = get_timezones_with_hour(1)
        assert result == ['Europe/Warsaw']

        result = get_timezones_with_hour(20)
        assert result == ['America/Sao_Paulo']

    @pytest.mark.freeze_time(test_date_time2)
    def test_now_mock_3(self):
        """Test ommitting not existing regions."""

        Business.objects.get(region__time_zone_name='Europe/Helsinki').delete()
        Region.objects.get(time_zone_name='Europe/Helsinki').delete()

        result = get_timezones_with_hour(10)
        assert result == []

        result = get_timezones_with_hour(2)
        assert result == ['Indian/Comoro']

        result = get_timezones_with_hour(19)
        assert result == ['America/New_York']

        result = get_timezones_with_hour(1)
        assert result == ['Europe/Warsaw']

        result = get_timezones_with_hour(20)
        assert result == ['America/Sao_Paulo']
