import logging
import typing as t
from functools import partial
from urllib.parse import urljoin

import requests
import responses
from django.conf import settings
from requests import Response

from lib.deeplink.consts import BranchIOAppTypes


logger = logging.getLogger('booksy.branch-io')


def generate_country_lang_url(url):
    return urljoin(url, f'{settings.LANGUAGE_CODE[:2]}-{settings.API_COUNTRY}/')


APP_URLS = {
    BranchIOAppTypes.BUSINESS: generate_country_lang_url(settings.BIZ_WEB_2_APP_URL),
    BranchIOAppTypes.CUSTOMER: generate_country_lang_url(settings.MARKETPLACE_URL),
    BranchIOAppTypes.BOOKSY_BUSINESS_FRONTDESK: generate_country_lang_url(
        settings.FRONTDESK_APP_URL
    ),
}
BRANCH_IO_KEYS_DEV = {
    BranchIOAppTypes.BUSINESS: settings.BRANCH_IO_KEY_BIZ_DEV,
    BranchIOAppTypes.CUSTOMER: settings.BRANCH_IO_KEY_CUS_DEV,
    BranchIOAppTypes.BOOKSY_BUSINESS_FRONTDESK: settings.BRANCH_IO_KEY_SAL_DEV,
}
BRANCH_IO_KEYS_PRD = {
    BranchIOAppTypes.BUSINESS: settings.BRANCH_IO_KEY_BIZ_PRD,
    BranchIOAppTypes.CUSTOMER: settings.BRANCH_IO_KEY_CUS_PRD,
    BranchIOAppTypes.BOOKSY_BUSINESS_FRONTDESK: settings.BRANCH_IO_KEY_SAL_PRD,
}


class BranchIOClient:
    BRANCHIO_CUSTOM_EVENT_URL = 'https://api2.branch.io/v2/event/custom'
    _valid_methods = {
        responses.GET,
        responses.POST,
        responses.PUT,
    }

    @classmethod
    def request_api(  # pylint: disable=too-many-arguments
        cls,
        url: str,
        app_type: BranchIOAppTypes,
        fields: dict,
        method: str = responses.POST,
        force_prod_keys: bool = True,
        **kwargs,
    ) -> t.Optional[Response]:
        """
        Documentation: https://docs.branch.io/pages/links/integrate/#configure-deep-links  # noqa
        """
        # Restore when branch-io start working on iOS
        # branch_io_keys = BRANCH_IO_KEYS_DEV
        # if settings.LIVE_DEPLOYMENT:
        #     branch_io_keys = BRANCH_IO_KEYS_PRD
        branch_io_keys = BRANCH_IO_KEYS_DEV
        if force_prod_keys or settings.LIVE_DEPLOYMENT:
            branch_io_keys = BRANCH_IO_KEYS_PRD

        try:
            branch_key = branch_io_keys[app_type]
        except KeyError:
            logger.exception('Wrong type of app provided: %s.', app_type)
            return None

        _fields = {
            'branch_key': branch_key,
        }
        _fields.update(fields)

        extra_keys = set(kwargs.keys()).difference(
            {'campaign', 'feature', 'channel', 'tags', 'stage', 'alias'}
        )

        if extra_keys:
            logger.exception('Wrong parameters provided: %s.', ', '.join(extra_keys))
            return None
        _fields.update(kwargs)

        try:
            branch_io_func = cls.get_request_method(method)
            response = branch_io_func(
                url=url,
                json=_fields,
            )
        except requests.exceptions.RequestException as error:
            logger.exception('Branch_io request error: %s', error)
            return None

        return response if (200 <= response.status_code < 300) else None

    @classmethod
    def get_request_method(cls, method: str = responses.POST) -> t.Callable:
        if settings.PYTEST:
            raise SyntaxWarning(
                'This method must be explicitly mocked with '
                '@pytest.mark.usefixtures("deeplink_request_mock")'
            )
        if method in cls._valid_methods:
            return partial(
                requests.request,
                method=method,
                timeout=3.0,
            )

    @classmethod
    def track_event(  # SEGMENT analytic
        cls, event_name: str, event_data: dict, user_data: dict, app_type: BranchIOAppTypes
    ):
        request_data = {
            'name': event_name,
            'custom_data': event_data,
            'user_data': user_data,
        }
        return cls.request_api(
            url=cls.BRANCHIO_CUSTOM_EVENT_URL,
            app_type=app_type,
            fields=request_data,
            force_prod_keys=False,
        )
