from django.conf import settings
from facebook_business.adobjects.serverside.action_source import ActionSource
from facebook_business.adobjects.serverside.custom_data import CustomData
from facebook_business.api import FacebookAdsApi
from facebook_business.adobjects.serverside.event import Event
from facebook_business.adobjects.serverside.event_request import EventRequest
from facebook_business.adobjects.serverside.user_data import UserData
from facebook_business.exceptions import FacebookRequestError
from lib.facebook.enums import EventName


class FacebookConversionAPIClient:
    """
    Class for sending conversion events to the Facebook Conversion API.

    Args:
        pixel_id (str): The Facebook pixel ID to which events will be sent.
    """

    def __init__(self, pixel_id: str):
        self.pixel_id = pixel_id
        FacebookAdsApi.init(access_token=settings.FB_CONVERSION_API_TOKEN)

    # pylint: disable=too-many-arguments
    def send_event(
        self,
        event_name: EventName,
        event_time: int,
        user_data: UserData,
        custom_data: CustomData = None,
        action_source: ActionSource = ActionSource.WEBSITE,
    ) -> dict:
        """
        Sends a conversion event to the Facebook Conversion API.

        Args:
            event_name (str): The name of the conversion event -
                              enum is serialized by celery to str.
            event_time (int): The time of the event in UNIX timestamp format.
            user_data (UserData): User data associated with the event.
            custom_data (CustomData): Other data, not associated with user
            action_source (ActionSource, optional): The event source (default is WEBSITE).

        Returns:
            dict: Response from the Facebook Conversion API.

        Raises:
            Exception: If there is an error in sending the event.
        """
        if settings.PYTEST:
            return {}

        fb_event = Event(
            event_name=event_name,
            event_time=event_time,
            user_data=user_data,
            custom_data=custom_data,
            action_source=action_source,
        )

        fb_event_request = EventRequest(
            events=[fb_event],
            pixel_id=self.pixel_id,
        )

        try:
            response = fb_event_request.execute()
            return response
        except FacebookRequestError as fb_error:
            error_message = fb_error.api_error_message()
            error_code = fb_error.api_error_code()
            raise Exception(  # pylint: disable=broad-exception-raised
                f"Error sending event to Facebook Conversion API:{error_message} Code:{error_code})"
            ) from fb_error
