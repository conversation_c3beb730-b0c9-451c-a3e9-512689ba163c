import tornado

from django.apps import apps
from django.core.exceptions import ValidationError
from django.core import validators
from django.db.models.fields import BigIntegerField
from django.forms.fields import Field, ChoiceField
from django.utils.encoding import smart_str
from django.utils.translation import gettext_lazy as _

from .date_time_infinity_field import DateTimeInfinityField
from .typed_list_field import TypedList<PERSON>ield
from .date_time_interval import DatetimeIntervalField
from .geo_location import GeoLocationField, GeoLocationViewportField
from .phone_number import BooksyPhoneNumberFormField
from .object_dict import ObjectDictField, ObjectDictListField


class ObjectIDField(Field):
    """Field that validates object ID."""

    default_error_messages = {
        'does_not_exist': 'Object does not exist',
    }

    # pylint: disable=keyword-arg-before-vararg, too-many-arguments
    def __init__(
        self, app=None, model=None, raise404=True, exception_codes=None, attrs=None, *args, **kwargs
    ):
        super().__init__(*args, **kwargs)
        self.app = app
        self.model = model
        self.raise404 = raise404
        # some fields may return 'invalid' instead of 'does_not_exist'
        # This dict gives a the means to support them
        self.exception_codes = {} if exception_codes is None else exception_codes
        # additional attributes that the returned object must satisfy
        self.attrs = {} if attrs is None else attrs

    def clean(self, value):
        if not value:
            if self.required:
                self.raise_exception(exception_code='required')
            else:
                return None

        try:
            value = int(value)
        except (ValueError, TypeError):
            self.raise_exception(exception_code='invalid')

        try:
            fetched_model = apps.get_model(self.app, self.model)

            return fetched_model.objects.get(id=value, **self.attrs)
        except fetched_model.DoesNotExist:
            self.raise_exception(exception_code='does_not_exist')

    def error_template(self, code='required', error_type='validation', description=''):
        default_error_msg = '%s is required' % self.label
        description = description or self.error_messages.get(code, default_error_msg)
        return description, {
            'code': code,
            'type': error_type,
        }

    def raise_exception(self, exception_code='required'):
        if self.raise404:
            raise tornado.web.HTTPError(404)

        code = self.exception_codes.get(exception_code, exception_code)
        description, params = self.error_template(code=code)
        raise ValidationError(description, params=params)


class ObjectIDListField(ObjectIDField):
    def clean(self, value):
        if not value:
            if self.required:
                self.raise_exception(exception_code='required')
            else:
                return None

        try:
            value = list(map(int, value))
        except (ValueError, TypeError):
            self.raise_exception(exception_code='invalid')

        model = apps.get_model(self.app, self.model)
        objects = model.objects.filter(id__in=value)

        if not objects.count() == len(value):
            self.raise_exception(exception_code='does_not_exist')

        return objects


class APIChoiceField(ChoiceField):
    """A ChoiceField that uses ValidationError(code='invalid') on errors.

    Django ChoiceField uses ValidationError(code='required') when user
    enters a value that is not in self.choices.
    API requires the code to be ='invalid', so we need to change that.

    Also, if field is not required and has an initial value, it is used
    on clean.

    Also, if you pass an choices in format ("choice1", "choice2", ...)
    it will be converted to standard (("choice1", "label1"), ...) format.

    """

    default_error_messages = {
        'invalid_choice': _(
            u'Select a valid choice. '
            '%(value)s is not one of the available choices. '
            'Available choices are: %(choices)s'
        ),
    }

    def __init__(self, *args, **kwargs):
        if 'choices' in kwargs:
            if any(
                not (isinstance(row, (tuple, list)) and len(row) == 2) for row in kwargs['choices']
            ):
                # transform it to standard django choices
                kwargs['choices'] = [(str(k), str(k)) for k in kwargs['choices']]
        super().__init__(*args, **kwargs)

    def validate(self, value):
        """Validates that the input is in self.choices."""
        # use super of ChoiceField, not of APIChoiceField
        Field.validate(self, value)
        if value and not self.valid_value(value):
            raise ValidationError(
                self.error_messages['invalid_choice']
                % {
                    'value': value,
                    'choices': [k for k, _ in self.choices],
                },
                code='invalid',
                params={'code': 'invalid'},
            )

    def to_python(self, value):
        if value is None:
            return self.initial
        return value


class APIIntegerChoiceField(APIChoiceField):
    """The same as APIChoiceField, but choices are assumed to be int."""

    def valid_value(self, value):
        "Check to see if the provided value is a valid choice"
        for k, v in self.choices:
            if isinstance(v, (list, tuple)):
                # This is an optgroup, so look inside the group for options
                for k_2, _ in v:
                    if value == int(k_2):
                        return True
            else:
                if value == int(k):
                    return True
        return False

    def clean(self, value):
        value = super().clean(value)
        if value in validators.EMPTY_VALUES:
            return self.initial
        return int(value)

    def to_python(self, value):
        try:
            return int(value)
        except (TypeError, ValueError):
            return value

    @staticmethod
    def get_prep_value(value):
        return smart_str(value)


class PositiveBigIntegerField(BigIntegerField):

    def formfield(self, **kwargs):
        return super().formfield(
            **{
                'min_value': 0,
                **kwargs,
            }
        )
