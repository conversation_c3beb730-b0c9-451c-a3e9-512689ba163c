from rest_framework import serializers
from django.contrib.postgres.forms import SimpleArrayField
from django.forms import IntegerField


class SimpleArraySerializerField(serializers.Field):
    def __init__(self, *args, **kwargs):
        super(SimpleArraySerializerField, self).__init__(*args, **kwargs)
        self.formfield = SimpleArrayField(IntegerField())

    def to_internal_value(self, data):
        return self.formfield.to_python(data)

    def to_representation(self, value):
        return self.formfield.get_prep_value(value)
