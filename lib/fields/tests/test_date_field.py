from datetime import date

from django.core.exceptions import ValidationError
from django.test import SimpleTestCase
from freezegun import freeze_time
from parameterized import parameterized

from lib.fields.date_field import MaxAgeValidator, MinAgeValidator

TODAY = date(year=2025, month=5, day=1)


@freeze_time(TODAY)
class MinAgeValidatorTests(SimpleTestCase):
    validator_class = MinAgeValidator

    def setUp(self) -> None:
        self.validator = self.validator_class(years=10)
        return super().setUp()

    @parameterized.expand(
        [
            (date(year=2015, month=5, day=1),),
            (date(year=1015, month=4, day=30),),
        ]
    )
    def test_valid_values(self, date_of_birth: date):
        self.assertIsNone(self.validator(date_of_birth))

    @parameterized.expand(
        [
            (date(year=2015, month=5, day=2),),
            (date(year=2025, month=5, day=15),),
        ]
    )
    def test_invalid_values(self, date_of_birth: date):
        self.assertRaisesMessage(
            ValidationError, "['You must be at least 10 years old.']", self.validator, date_of_birth
        )


@freeze_time(TODAY)
class MaxAgeValidatorTests(SimpleTestCase):
    validator_class = MaxAgeValidator

    def setUp(self) -> None:
        self.validator = self.validator_class(years=10)
        return super().setUp()

    @parameterized.expand(
        [
            (date(year=2015, month=5, day=1),),
            (date(year=2025, month=5, day=5),),
            (TODAY,),
        ]
    )
    def test_valid_values(self, date_of_birth: date):
        self.assertIsNone(self.validator(date_of_birth))

    @parameterized.expand(
        [
            (date(year=2015, month=4, day=30),),
            (date(year=1015, month=4, day=29),),
        ]
    )
    def test_invalid_values(self, date_of_birth: date):
        self.assertRaisesMessage(
            ValidationError,
            "['You must be less than 10 years old.']",
            self.validator,
            date_of_birth,
        )
