from django.contrib.auth.hashers import check_password
from django.core.exceptions import ValidationError

from django.test import TestCase
from parameterized import parameterized

from lib.fields.password import BooksyPassword<PERSON>erial<PERSON><PERSON>ield, BooksyPassword<PERSON><PERSON><PERSON>ield
from lib.validators import BooksyPasswordValidator


class TestBooksyPasswordSerializerField(TestCase):
    def setUp(self) -> None:
        self.booksy_password_serializer_field = BooksyPasswordSerializerField()
        super().setUp()

    @parameterized.expand(['StrongPassword1', '12345678a', 'strongpassword1@$%', "ひらがな12345"])
    def test_strong_password(self, strong_password):
        hashed_password = self.booksy_password_serializer_field.run_validation(strong_password)
        self.assertTrue(check_password(strong_password, hashed_password))

    @parameterized.expand(['ABCabcABC', '123254352364365', 'strongpass@$%$@$%', 'ひらがなひらがな'])
    def test_invalid_password(self, invalid_password):
        with self.assertRaisesRegex(ValidationError, str(BooksyPasswordValidator.HELP_TEXT)):
            self.booksy_password_serializer_field.run_validation(invalid_password)

    def test_password_too_short_error(self):
        with self.assertRaisesRegex(
            ValidationError, 'This password is too short. It must contain at least 8 characters.'
        ):
            self.booksy_password_serializer_field.run_validation('abc@')

    def test_password_invalid(self):
        with self.assertRaises(ValidationError) as password_exception:
            self.booksy_password_serializer_field.run_validation('abc')

        self.assertIn(
            'This password is too short. It must contain at least 8 characters.',
            password_exception.exception,
        )
        self.assertIn(BooksyPasswordValidator.HELP_TEXT, password_exception.exception)


class TestBooksyPasswordFormField(TestCase):
    def setUp(self) -> None:
        self.booksy_password_serializer_field = BooksyPasswordFormField()
        super().setUp()

    @parameterized.expand(['StrongPassword1', '12345678abc', 'strongpassword1@$%'])
    def test_strong_password(self, strong_password):
        unchanged_password_after_clean = self.booksy_password_serializer_field.clean(
            strong_password
        )
        self.assertTrue(strong_password, unchanged_password_after_clean)

    @parameterized.expand(['ABCabcABC', '123254352364365@#', 'strongpassword#^$%%'])
    def test_invalid_password(self, invalid_password):
        with self.assertRaisesRegex(ValidationError, str(BooksyPasswordValidator.HELP_TEXT)):
            self.booksy_password_serializer_field.clean(invalid_password)

    def test_password_too_short_error(self):
        with self.assertRaises(ValidationError) as password_exception:
            self.booksy_password_serializer_field.clean('abc@')

        self.assertIn(
            'Ensure this value has at least 8 characters (it has 4).',
            password_exception.exception,
        )
