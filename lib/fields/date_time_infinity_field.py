from datetime import datetime, MAXYEAR

from django.db.models.fields import DateTimeField

from lib.tools import datetimeinfinity


class DateTimeInfinityField(DateTimeField):

    def to_python(self, value):
        value = super().to_python(value)

        # Crude but true ;)
        if isinstance(value, datetime) and value.year == MAXYEAR:
            value = datetimeinfinity()

        return value

    def from_db_value(self, value, *_args):
        return self.to_python(value)
