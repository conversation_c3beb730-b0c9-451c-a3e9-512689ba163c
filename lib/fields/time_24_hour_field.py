from django.db.models.fields import TimeField
from django.utils.dateparse import time_re

from lib.time_24_hour import time24hour


class Time24HourField(TimeField):
    def db_type(self, connection):
        return 'time without time zone'

    def to_python(self, value):
        if value is None:
            return

        if isinstance(value, time24hour):
            return value

        value = super().to_python(value)

        return time24hour(value.hour, value.minute, value.second, value.microsecond)

    def from_db_value(self, value, *_args):
        return self.to_python(value)


# modified version of django.utils.dateparse.parse_time
def parse_time(value):
    match = time_re.match(value)
    if match:
        k_w = match.groupdict()
        if k_w['microsecond']:
            k_w['microsecond'] = k_w['microsecond'].ljust(6, '0')
        k_w = dict((k, int(v)) for k, v in list(k_w.items()) if v is not None)
        return time24hour(**k_w)
