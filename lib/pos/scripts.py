from django.db import transaction

from lib.db import PAYMENTS_DB
from webapps.commission.scripts import (
    clear_commission_data_for_business_ids,
    clear_migrated_commission_data_for_business_ids,
)
from webapps.invoicing.scripts import clear_invoicing_reference_data_for_business_ids
from webapps.payment_gateway.scripts import (
    clear_migrated_payment_gateway_data_for_business_ids,
    clear_wallet_data_for_business_ids,
)
from webapps.payment_providers.scripts import clear_payment_providers_data_for_business_ids
from webapps.point_of_sale.scripts import (
    clear_migrated_pos_data_for_business_ids,
    clear_payment_data_for_business_ids,
)
from webapps.pos.scripts import (
    clear_migrated_pos_reference_data_for_business_ids,
    clear_pos_reference_data_for_business_ids,
)


class DryException(Exception):
    pass


def clear_refactor_data_for_business_ids(business_ids: list[int], dry_run: bool = True):
    with transaction.atomic(), transaction.atomic(using=PAYMENTS_DB):
        clear_payment_data_for_business_ids(business_ids=business_ids)
        clear_invoicing_reference_data_for_business_ids(business_ids=business_ids)
        clear_commission_data_for_business_ids(business_ids=business_ids)
        clear_pos_reference_data_for_business_ids(business_ids=business_ids)
        clear_payment_providers_data_for_business_ids(business_ids=business_ids, confirm=True)
        clear_wallet_data_for_business_ids(business_ids=business_ids)

        if dry_run:
            raise DryException('Dry run flag enabled! Reverting.')


def clear_migrated_refactor_data_for_business_ids(business_ids: list[int], dry_run: bool = True):
    with transaction.atomic(), transaction.atomic(using=PAYMENTS_DB):
        # Delete references
        clear_migrated_pos_reference_data_for_business_ids(business_ids=business_ids)

        # Delete objects
        clear_migrated_payment_gateway_data_for_business_ids(business_ids=business_ids)
        clear_migrated_pos_data_for_business_ids(business_ids=business_ids)
        clear_migrated_commission_data_for_business_ids(business_ids=business_ids)

        if dry_run:
            raise DryException('Dry run flag enabled! Reverting.')
