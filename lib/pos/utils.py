from webapps.pos.enums import POSMigrationGroup


def pos_refactor_stage2_enabled(pos) -> bool:
    """
    Returns True if a given POS instance has stage2 enabled
    (new structure syncs with the old one)
    """
    if not pos:
        return False
    return pos.pos_refactor_stage2_enabled


def txn_refactor_stage2_enabled(txn: 'Transaction') -> bool:
    """
    Returns True if given transaction instance should be handled in new structure.
    If transaction was created before migration date of POS and doesn't have basket_id
    (is not migrated yet) it means that is transaction only in old structure.

    If there's no migration date or the POS has been moved in the ONLINE_DATA_TRANSACTION group,
    just check if pos_refactor_stage2_enabled is on.
    """
    from webapps.pos.models import Transaction

    if not txn:
        return False

    migration_date = txn.pos.pos_refactor_stage2_migration_date
    if (
        not migration_date
        or txn.pos.pos_refactor_stage2_migration_group == POSMigrationGroup.ONLINE_DATA_TRANSACTION
    ):
        return pos_refactor_stage2_enabled(txn.pos)

    first_txn_in_series = (
        txn if txn.series_id == txn.id else Transaction.objects.filter(id=txn.series_id).last()
    )

    if not first_txn_in_series.basket_id and migration_date > first_txn_in_series.created:
        return False

    return pos_refactor_stage2_enabled(txn.pos)
