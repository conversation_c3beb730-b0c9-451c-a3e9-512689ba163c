import unittest

from django.test import override_settings
from django.utils.translation import gettext_lazy as _

import pytest
from model_bakery import baker

from country_config import Country
from webapps.business.models import Business
from webapps.pos.models import POS, PaymentRow, Receipt, Transaction
from webapps.stripe_integration.enums import StripeExternalAccountType, StripePayoutMethodType
from webapps.stripe_integration.exceptions import ReaderNotFound
from webapps.stripe_integration.provider import StripeProvider, StripeProxyProvider
from webapps.stripe_integration.models import StripePaymentIntent
from webapps.stripe_integration.tests.mocks import (
    mock_stripe_reader_modify,
    mock_stripe_reader_retrieve,
    mock_stripe_account_retrieve,
)
from webapps.structure.baker_recipes import bake_region_graphs
from webapps.structure.models import Region


@pytest.mark.parametrize(
    'business_name',
    [
        ("very long name that should be trimmed", "very long name that sh"),
        (r"very l>o<n\g na'me th\"at *sh", "very long name that sh"),
        ("abc", None),
        ("abc><*", None),
        ("Correct salon name", "Correct salon name"),
    ],
)
@pytest.mark.django_db
def test_generate_stripe_statement_descriptor(business_name):
    business = baker.make(
        Business,
        name=business_name[0],
    )
    pos = baker.make(POS, business=business)

    expected_name = business_name[1]
    if expected_name is None:
        # use the default name
        expected_name = _("Salon {business_id}").format(business_id=business.id)

    assert StripeProvider.generate_stripe_statement_descriptor(pos) == expected_name


@pytest.mark.django_db
def test_get_state_code():
    zipcode = baker.make(Region, name='08701', type=Region.Type.ZIP)
    city = baker.make(Region, name='City', type=Region.Type.CITY)
    region = baker.make(Region, name='Region', type=Region.Type.REGION, abbrev='Region Abbrev')
    state = baker.make(Region, name='State', type=Region.Type.STATE, abbrev='State Abbrev')
    bake_region_graphs(state, region, city, zipcode)
    business = baker.make(Business, region=zipcode)

    abbrev = StripeProvider._get_stripe_state_code(business)  # pylint: disable=protected-access
    assert abbrev == 'State Abbrev'


@override_settings(API_COUNTRY=Country.ES)
@pytest.mark.django_db
def test_get_state_code_for_spain():
    zipcode = baker.make(Region, name='08701', type=Region.Type.ZIP)
    city = baker.make(Region, name='City', type=Region.Type.CITY)
    region_1 = baker.make(Region, name='Region 1', type=Region.Type.REGION)
    region_2 = baker.make(Region, name='Region 2', type=Region.Type.REGION, abbrev='Region Abbrev')
    state = baker.make(Region, name='New Jersey', type=Region.Type.STATE, abbrev='State Abbrev')
    bake_region_graphs(state, region_2, region_1, city, zipcode)
    business = baker.make(Business, region=zipcode)

    abbrev = StripeProvider._get_stripe_state_code(business)  # pylint: disable=protected-access
    assert abbrev == 'Region Abbrev'


@pytest.mark.django_db
class TestUpdateStripeReaderLabel(unittest.TestCase):
    def setUp(self):
        super().setUp()
        self.business = baker.make("business.Business")
        self.pos = baker.make("pos.POS", business=self.business)
        self.account = baker.make(
            "stripe_integration.StripeAccount",
            external_id="123",
            pos=self.pos,
        )
        self.location = baker.make(
            "stripe_integration.StripeLocation",
            name="name",
            default=True,
            account=self.account,
            external_id="correct_location_id",
        )

    @mock_stripe_reader_retrieve
    def test_non_existing_reader_id(self, mock_retrieve):
        try:
            StripeProvider.update_stripe_reader_label(
                label="new label",
                reader_id="does_not_exist",
                stripe_account=self.account,
            )
            assert False, "Exception not raised"
        except ReaderNotFound:
            pass

    @mock_stripe_reader_retrieve
    def test_incorrect_location(self, mock_retrieve):
        try:
            StripeProvider.update_stripe_reader_label(
                label="new label",
                reader_id="incorrect_location",
                stripe_account=self.account,
            )
            assert False, "Exception not raised"
        except ReaderNotFound:
            pass

    @mock_stripe_reader_retrieve
    @mock_stripe_reader_modify
    def test_correct(self, mock_modify, mock_retrieve):
        StripeProvider.update_stripe_reader_label(
            label="new label",
            reader_id="correct_location_id",
            stripe_account=self.account,
        )

        assert mock_modify.called

    @mock_stripe_account_retrieve(StripeExternalAccountType.CARD, None)
    def test_get_available_payout_methods(self, mock_account_retrieve):
        methods = StripeProvider.get_available_payout_methods(self.account)

        assert mock_account_retrieve.called
        self.assertEqual(len(methods), 2)
        self.assertIn(StripePayoutMethodType.STANDARD, methods)
        self.assertIn(StripePayoutMethodType.INSTANT, methods)

    @mock_stripe_account_retrieve(StripeExternalAccountType.CARD, [StripePayoutMethodType.INSTANT])
    def test_get_available_payout_methods_instant(self, mock_account_retrieve):
        methods = StripeProvider.get_available_payout_methods(self.account)

        assert mock_account_retrieve.called
        self.assertEqual(len(methods), 1)
        self.assertIn(StripePayoutMethodType.INSTANT, methods)

    @override_settings(API_COUNTRY=Country.GB)
    @mock_stripe_account_retrieve(
        StripeExternalAccountType.BANK_ACCOUNT, [StripePayoutMethodType.STANDARD]
    )
    def test_get_available_payout_methods_standard(self, mock_account_retrieve):
        methods = StripeProvider.get_available_payout_methods(self.account)

        assert mock_account_retrieve.called
        self.assertEqual(len(methods), 1)
        self.assertIn(StripePayoutMethodType.STANDARD, methods)


@pytest.mark.django_db
class TestTTPErrorMessages:
    """Test TTP error message handling for POS-4244 follow-up issue."""

    def test_stripe_provider_add_payment_method_err_msg_with_ttp_error(self):
        """Test that StripeProvider returns correct TTP error message."""
        # Create payment row with associated payment intent that has TTP error
        payment_row = baker.make(PaymentRow)
        payment_intent = baker.make(
            StripePaymentIntent,
            error_code="card_read_timed_out"
        )
        payment_row.intents.add(payment_intent)

        # Call StripeProvider method
        result = StripeProvider.add_payment_method_err_msg(payment_row)

        # Should return the specific TTP error message
        expected_msg = "Payment could not be completed because the transaction timed out, please try again."
        assert result['msg'] == expected_msg
        assert result['type'] == PaymentRow.PAYMENT_ROW__REJECT_REASON

    def test_stripe_proxy_provider_add_payment_method_err_msg_with_ttp_error_after_fix(self):
        """Test that StripeProxyProvider returns correct TTP error message after fix."""
        # Create payment row with associated payment intent that has TTP error
        payment_row = baker.make(PaymentRow)
        payment_intent = baker.make(
            StripePaymentIntent,
            error_code="card_read_timed_out"
        )
        payment_row.intents.add(payment_intent)

        # Call StripeProxyProvider method (now overridden to use same logic as StripeProvider)
        result = StripeProxyProvider.add_payment_method_err_msg(payment_row)

        # After fix: Should return the specific TTP error message
        expected_msg = "Payment could not be completed because the transaction timed out, please try again."
        assert result['msg'] == expected_msg
        assert result['type'] == PaymentRow.PAYMENT_ROW__REJECT_REASON

    def test_stripe_proxy_provider_add_payment_method_err_msg_with_unknown_error(self):
        """Test that StripeProxyProvider returns default message for unknown error codes."""
        # Create payment row with associated payment intent that has unknown error
        payment_row = baker.make(PaymentRow)
        payment_intent = baker.make(
            StripePaymentIntent,
            error_code="unknown_error_code"
        )
        payment_row.intents.add(payment_intent)

        # Call StripeProxyProvider method
        result = StripeProxyProvider.add_payment_method_err_msg(payment_row)

        # Should return default message for unknown error codes
        expected_msg = "Something went wrong!"
        assert result['msg'] == expected_msg
        assert result['type'] == PaymentRow.PAYMENT_ROW__REJECT_REASON

    def test_stripe_proxy_provider_add_payment_method_err_msg_no_payment_intent(self):
        """Test that StripeProxyProvider returns default message when no payment intent exists."""
        # Create payment row without associated payment intent
        payment_row = baker.make(PaymentRow)

        # Call StripeProxyProvider method
        result = StripeProxyProvider.add_payment_method_err_msg(payment_row)

        # Should return default message when no payment intent
        expected_msg = "Something went wrong!"
        assert result['msg'] == expected_msg
        assert result['type'] == PaymentRow.PAYMENT_ROW__REJECT_REASON
