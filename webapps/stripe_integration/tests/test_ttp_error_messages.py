"""
Test for TTP error message handling in stage2 environments.

This test reproduces and verifies the fix for the issue described in the follow-up
to ticket POS-4244, where TTP error messages were not being displayed correctly
in stage2 environments because StripeProxyProvider was using the base ProxyProvider
logic instead of the StripeProvider logic for error message handling.
"""

import pytest
from model_bakery import baker
from django.utils.translation import gettext_lazy as _

from webapps.pos.models import PaymentRow, Receipt, Transaction
from webapps.pos.serializers import ReceiptNoteSerializer
from webapps.pos.enums import receipt_status, PaymentProviderEnum
from webapps.stripe_integration.models import StripePaymentIntent
from webapps.stripe_integration.provider import StripeProvider, StripeProxyProvider


@pytest.mark.django_db
class TestTTPErrorMessagesIntegration:
    """Integration tests for TTP error message handling."""

    def test_receipt_note_serializer_with_stripe_provider(self):
        """Test ReceiptNoteSerializer with StripeProvider (stage1 behavior)."""
        # Create a failed payment row with TTP error
        payment_row = baker.make(
            PaymentRow,
            status=receipt_status.PAYMENT_FAILED,
            provider=PaymentProviderEnum.STRIPE_PROVIDER.value
        )
        payment_intent = baker.make(
            StripePaymentIntent,
            error_code="card_read_timed_out"
        )
        payment_row.intents.add(payment_intent)
        
        # Create receipt with the failed payment row
        receipt = baker.make(Receipt)
        payment_row.receipt = receipt
        payment_row.save()
        
        # Serialize the receipt note
        serializer = ReceiptNoteSerializer()
        result = serializer.to_representation(receipt)
        
        # Should return the specific TTP error message
        expected_msg = "Payment could not be completed because the transaction timed out, please try again."
        assert result['msg'] == expected_msg
        assert result['type'] == PaymentRow.PAYMENT_ROW__REJECT_REASON

    def test_receipt_note_serializer_with_stripe_proxy_provider_after_fix(self):
        """Test ReceiptNoteSerializer with StripeProxyProvider (stage2 behavior after fix)."""
        # Create a failed payment row with TTP error using proxy provider
        payment_row = baker.make(
            PaymentRow,
            status=receipt_status.PAYMENT_FAILED,
            provider=PaymentProviderEnum.STRIPE_PROXY_PROVIDER.value
        )
        payment_intent = baker.make(
            StripePaymentIntent,
            error_code="card_read_timed_out"
        )
        payment_row.intents.add(payment_intent)
        
        # Create receipt with the failed payment row
        receipt = baker.make(Receipt)
        payment_row.receipt = receipt
        payment_row.save()
        
        # Serialize the receipt note
        serializer = ReceiptNoteSerializer()
        result = serializer.to_representation(receipt)
        
        # After fix: Should return the specific TTP error message
        expected_msg = "Payment could not be completed because the transaction timed out, please try again."
        assert result['msg'] == expected_msg
        assert result['type'] == PaymentRow.PAYMENT_ROW__REJECT_REASON

    def test_receipt_note_serializer_with_other_ttp_errors(self):
        """Test ReceiptNoteSerializer with other TTP error codes."""
        test_cases = [
            ("tap_to_pay_device_tampered", "We detected a possible security issue. This could happen if parts of your device have been replaced or if the device's software was modified."),
            ("tap_to_pay_nfc_disabled", "NFC must be enabled to process Tap to Pay transactions. Please check your settings and try again."),
            ("location_services_disabled", "Location services must be enabled to process Tap to Pay transactions. Please check your settings and try again."),
            ("tap_to_pay_insecure_environment", "We detected a possible security issue. Please ensure screen recording is off, camera is not active, and developer options are disabled in your settings."),
        ]
        
        for error_code, expected_message in test_cases:
            # Create a failed payment row with specific TTP error
            payment_row = baker.make(
                PaymentRow,
                status=receipt_status.PAYMENT_FAILED,
                provider=PaymentProviderEnum.STRIPE_PROXY_PROVIDER.value
            )
            payment_intent = baker.make(
                StripePaymentIntent,
                error_code=error_code
            )
            payment_row.intents.add(payment_intent)
            
            # Create receipt with the failed payment row
            receipt = baker.make(Receipt)
            payment_row.receipt = receipt
            payment_row.save()
            
            # Serialize the receipt note
            serializer = ReceiptNoteSerializer()
            result = serializer.to_representation(receipt)
            
            # Should return the specific TTP error message
            assert result['msg'] == expected_message
            assert result['type'] == PaymentRow.PAYMENT_ROW__REJECT_REASON

    def test_receipt_note_serializer_with_no_failed_payment_rows(self):
        """Test ReceiptNoteSerializer when there are no failed payment rows."""
        # Create a successful payment row
        payment_row = baker.make(
            PaymentRow,
            status=receipt_status.PAYMENT_CAPTURED,
            provider=PaymentProviderEnum.STRIPE_PROXY_PROVIDER.value
        )
        
        # Create receipt with the successful payment row
        receipt = baker.make(Receipt)
        payment_row.receipt = receipt
        payment_row.save()
        
        # Serialize the receipt note
        serializer = ReceiptNoteSerializer()
        result = serializer.to_representation(receipt)
        
        # Should return empty result since no failed payment rows
        assert result == {}

    def test_receipt_note_serializer_with_failed_payment_no_error_code(self):
        """Test ReceiptNoteSerializer with failed payment but no error code."""
        # Create a failed payment row without error code
        payment_row = baker.make(
            PaymentRow,
            status=receipt_status.PAYMENT_FAILED,
            provider=PaymentProviderEnum.STRIPE_PROXY_PROVIDER.value
        )
        payment_intent = baker.make(
            StripePaymentIntent,
            error_code=None
        )
        payment_row.intents.add(payment_intent)
        
        # Create receipt with the failed payment row
        receipt = baker.make(Receipt)
        payment_row.receipt = receipt
        payment_row.save()
        
        # Serialize the receipt note
        serializer = ReceiptNoteSerializer()
        result = serializer.to_representation(receipt)
        
        # Should return default message when no error code
        expected_msg = "Something went wrong!"
        assert result['msg'] == expected_msg
        assert result['type'] == PaymentRow.PAYMENT_ROW__REJECT_REASON
